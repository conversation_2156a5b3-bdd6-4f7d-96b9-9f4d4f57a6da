"use client";

import React, { useState } from "react";

interface FAQItem {
  id: number;
  question: string;
  answer?: string;
}

const FAQSection: React.FC = () => {
  const [openItem, setOpenItem] = useState<number | null>(2);

  const faqItems: FAQItem[] = [
    {
      id: 1,
      question: "Is Zy<PERSON> a bank?",
    },
    {
      id: 2,
      question: "Is this a crypto app?",
      answer:
        "No, Zybra is not crypto. We use secure global distributed financial technology to give you a digital dollar account — a simple, virtual way to save and grow real U.S. dollars.",
    },
    {
      id: 3,
      question: "Where is my money allocated?",
    },
    {
      id: 4,
      question: "Can I lose money?",
    },
    {
      id: 5,
      question: "How do I start?",
    },
  ];

  const toggleItem = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <section className="py-16 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 text-center mb-12">
          Frequently asked questions
        </h2>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqItems.map((item) => (
            <div
              key={item.id}
              className="bg-gray-50 rounded-2xl overflow-hidden"
            >
              <button
                onClick={() => toggleItem(item.id)}
                className="w-full flex items-center justify-between p-6 text-left focus:outline-none"
              >
                <span className="text-lg font-medium text-gray-700">
                  {item.question}
                </span>
                <span className="ml-4 flex-shrink-0">
                  {openItem === item.id ? (
                    <span className="text-2xl text-red-400 font-light">−</span>
                  ) : (
                    <span className="text-2xl text-gray-600 font-light">+</span>
                  )}
                </span>
              </button>
              {openItem === item.id && item.answer && (
                <div className="px-6 pb-6">
                  <p className="text-gray-600 leading-relaxed">{item.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
