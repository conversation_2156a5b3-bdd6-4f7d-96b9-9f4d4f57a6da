import React from "react";

const WhyChooseZybra = () => {
  return (
    <div className="bg-[#1B5E4B] py-20 px-8 md:px-16 lg:px-24">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-white text-5xl md:text-6xl font-normal mb-3">
            Why choose Zybra
          </h2>
          <p className="text-white/80 text-xl">
            Trusted digital Dollar account for Africa.
          </p>
        </div>

        {/* Bento Grid Layout */}
        <div className="grid grid-cols-4 gap-5 max-w-6xl mx-auto">
          {/* Row 1 */}
          {/* Save in Real U.S. Dollars - small card */}
          <div className="bg-white rounded-3xl p-6 col-span-1">
            <h3 className="text-2xl font-normal mb-4 leading-tight">
              Save in Real
              <br />
              U.S. Dollars
            </h3>
            <p className="text-gray-600 text-sm">
              Protect your savings
              <br />
              from inflation
            </p>
          </div>

          {/* Always Accessible - small card */}
          <div className="bg-[#F5E6D3] rounded-3xl p-6 col-span-1">
            <h3 className="text-2xl font-normal mb-4 leading-tight">
              Always
              <br />
              Accessible
            </h3>
            <p className="text-gray-600 text-sm">
              USSD, mobile app,
              <br />
              and web access.
            </p>
          </div>

          {/* No Bank Account Needed - tall card with phone */}
          <div className="bg-[#D4A850] rounded-3xl p-6 col-span-2 row-span-2 relative overflow-hidden">
            <h3 className="text-3xl font-normal mb-4 leading-tight">
              No Bank Account
              <br />
              Needed
            </h3>
            <p className="text-gray-800 text-base">
              Skip paperwork, queues,
              <br />
              and minimum deposits
            </p>
            {/* Phone placeholder */}
            <div className="absolute bottom-0 right-4 w-40 h-52">
              <div className="bg-black/10 rounded-t-2xl w-full h-full"></div>
            </div>
          </div>

          {/* Row 2 */}
          {/* Zybra Logo Card - wide */}
          <div className="bg-[#2A2A2A] rounded-3xl p-8 col-span-2 flex items-center justify-center">
            <div className="flex items-center gap-4">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path
                  d="M8 12C8 12 16 9 24 12C32 15 34 12 34 12"
                  stroke="white"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                />
                <path
                  d="M8 20C8 20 16 17 24 20C32 23 34 20 34 20"
                  stroke="white"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                />
                <path
                  d="M8 28C8 28 16 25 24 28C32 31 34 28 34 28"
                  stroke="white"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                />
              </svg>
              <span className="text-white text-4xl font-normal">Zybra</span>
            </div>
          </div>

          {/* Row 3 */}
          {/* Safe & Transparent - wide with button */}
          <div className="bg-[#F5E6D3] rounded-3xl p-8 col-span-2">
            <h3 className="text-3xl font-normal mb-4">Safe & Transparent</h3>
            <p className="text-gray-600 text-base mb-6">
              Powered by global financial markets
              <br />
              and secure technology.
            </p>
            <button className="bg-[#2A2A2A] text-white px-6 py-3 rounded-full text-sm">
              Start earning now
            </button>
          </div>

          {/* Instant Withdrawals - small card */}
          <div className="bg-white rounded-3xl p-6 col-span-1">
            <h3 className="text-2xl font-normal mb-4 leading-tight">
              Instant
              <br />
              Withdrawals
            </h3>
            <p className="text-gray-600 text-sm">
              Access your virtual
              <br />
              dollar account anytime.
            </p>
          </div>

          {/* Bottom phone card - dark */}
          <div className="bg-[#1A1A1A] rounded-3xl p-6 col-span-1 flex items-center justify-center">
            <div className="w-32 h-44 bg-white/5 rounded-xl"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhyChooseZybra;
