import React from "react";
import Image from "next/image";

const AccessibleAnywhereSection: React.FC = () => {
  return (
    <section className="py-16 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Accessible Anywhere
          </h2>
          <p className="text-xl text-gray-600">
            Access your mobile money anywhere.
          </p>
        </div>

        {/* Feature Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Mobile App Card */}
          <div className="bg-teal-800 rounded-2xl p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h3 className="text-2xl font-bold mb-2">Mobile App</h3>
              <p className="text-teal-200 mb-8">IOS & Android</p>
            </div>
            <div className="relative">
              <Image
                src="/mobile-app-mockup.png"
                alt="Mobile app interface"
                width={200}
                height={400}
                className="mx-auto"
              />
            </div>
          </div>

          {/* USSD Card */}
          <div className="bg-teal-800 rounded-2xl p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h3 className="text-2xl font-bold mb-2">USSD</h3>
              <p className="text-teal-200 mb-2">Works on any phone,</p>
              <p className="text-teal-200 mb-8">no internet required</p>
            </div>
            <div className="relative">
              <Image
                src="/ussd-phone-mockup.png"
                alt="USSD interface on basic phone"
                width={180}
                height={320}
                className="mx-auto"
              />
            </div>
          </div>

          {/* Web Dashboard Card */}
          <div className="bg-teal-800 rounded-2xl p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h3 className="text-2xl font-bold mb-2">Web Dashboard</h3>
              <p className="text-teal-200 mb-2">Track and manage</p>
              <p className="text-teal-200 mb-8">your account online</p>
            </div>
            <div className="relative">
              <Image
                src="/web-dashboard-mockup.png"
                alt="Web dashboard interface"
                width={280}
                height={200}
                className="mx-auto rounded-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AccessibleAnywhereSection;
