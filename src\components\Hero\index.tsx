import React from "react";

export default function LandingPage() {
  return (
    <div
      className="min-h-screen"
      style={{
        background: "linear-gradient(135deg, #B4E7D5 0%, #C8EADF 100%)",
      }}
    >
      <nav className="flex items-center justify-between px-10 md:px-16 lg:px-24 py-8">
        <div className="flex items-center gap-12">
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
            <path
              d="M6 10C6 10 12 8 18 10C24 12 26 10 26 10"
              stroke="#000"
              strokeWidth="2.5"
              strokeLinecap="round"
            />
            <path
              d="M6 16C6 16 12 14 18 16C24 18 26 16 26 16"
              stroke="#000"
              strokeWidth="2.5"
              strokeLinecap="round"
            />
            <path
              d="M6 22C6 22 12 20 18 22C24 24 26 22 26 22"
              stroke="#000"
              strokeWidth="2.5"
              strokeLinecap="round"
            />
          </svg>

          <div className="hidden md:flex items-center gap-10">
            <a href="#" className="text-gray-800 text-base">
              Docs
            </a>
            <a href="#" className="text-gray-800 text-base">
              Faq
            </a>
          </div>
        </div>

        <button className="bg-black text-white px-6 py-3 rounded-full text-sm flex items-center gap-2">
          Download App
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path
              d="M6 2v6m0 0L4 6m2 2l2-2"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </nav>

      <div className="px-10 md:px-16 lg:px-24 pt-20">
        <h1 className="text-6xl md:text-7xl lg:text-8xl font-normal leading-[0.95] mb-10">
          Virtual Dollar
          <br />
          Savings Account
        </h1>

        <p className="text-3xl md:text-4xl font-light mb-12">
          No Bank, No Internet Needed.
        </p>

        <p className="text-xl md:text-2xl text-gray-700 font-light leading-relaxed mb-12">
          Save and Grow Real U.S. Dollars up to 10%.
          <br />
          All From Your Mobile Money!
        </p>

        <div className="flex flex-wrap gap-4 mb-10">
          <button className="bg-black text-white px-6 py-4 rounded-full flex items-center gap-3 text-base">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor">
              <path d="M3.62 7.79L5.06 3.34C5.15 3.04 5.43 2.84 5.75 2.84H8.25C8.52 2.84 8.77 2.97 8.92 3.19C9.07 3.41 9.1 3.69 9 3.95L8.08 6.52C8.03 6.67 8.03 6.83 8.08 6.98C8.13 7.13 8.23 7.25 8.36 7.33L11.47 9.08C11.65 9.19 11.77 9.37 11.81 9.58L12.66 14.03C12.71 14.3 12.63 14.58 12.44 14.78C12.25 14.98 11.97 15.09 11.69 15.06L7.24 14.61C7.09 14.6 6.94 14.53 6.82 14.42L3.32 11.42C3.12 11.24 3 10.98 3 10.71V8.29C3 8.12 3.05 7.95 3.14 7.81C3.23 7.67 3.36 7.56 3.51 7.49L3.62 7.79Z" />
            </svg>
            Dial *334#
          </button>

          <button className="bg-white/50 backdrop-blur-sm border border-gray-300 text-black px-6 py-4 rounded-full flex items-center gap-3 text-base">
            Download App
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path
                d="M6 2v6m0 0L4 6m2 2l2-2"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        <p className="text-gray-600 text-base">
          Dial *334# or download the Zybra app to open your digital dollar
          account now.
        </p>
      </div>
    </div>
  );
}
