"use client";

import React, { useState, useRef } from "react";
import {
  Splide,
  SplideSlide,
  Splide as SplideClass,
} from "@splidejs/react-splide";
import "@splidejs/react-splide/css";

const HowItWorksSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const splideRef = useRef<SplideClass | null>(null); // use Splide class type

  const slides = [
    {
      title: "Connect Your Mobile Money",
      description:
        "Use your existing mobile number or mobile money account. No new sign-ups.",
      step: "01/04",
      phoneContent: "Phone Visual 1",
    },
    {
      title: "Open Dollar Account",
      description:
        "Create your virtual dollar savings account instantly with just a few taps.",
      step: "02/04",
      phoneContent: "Phone Visual 2",
    },
    {
      title: "Save & Grow",
      description:
        "Transfer money anytime and watch your savings grow up to 10% annually.",
      step: "03/04",
      phoneContent: "Phone Visual 3",
    },
    {
      title: "Access Anytime",
      description:
        "Withdraw or transfer your dollars back to mobile money whenever you need.",
      step: "04/04",
      phoneContent: "Phone Visual 4",
    },
  ];

  return (
    <div className="bg-gray-50 py-20">
      <div className="px-10 md:px-16 lg:px-24">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-normal mb-4">
            How Zybra Works
          </h2>
          <p className="text-xl md:text-2xl text-gray-600">
            Your Virtual Dollar Account in 4 Easy Steps
          </p>
        </div>

        {/* Slider Container */}
        <div className="max-w-4xl mx-auto">
          {/* Image Slider */}
          <Splide
            ref={splideRef}
            onMove={(splide) => setCurrentSlide(splide.index)}
            options={{
              type: "loop",
              perPage: 1,
              autoplay: true,
              interval: 4000,
              pauseOnHover: false,
              pauseOnFocus: false,
              speed: 800,
              arrows: false,
              pagination: false,
              drag: true,
            }}
          >
            {slides.map((slide, index) => (
              <SplideSlide key={index}>
                <div className="bg-[#1B5E4B] rounded-3xl p-8 md:p-12 min-h-[400px] flex items-center justify-center">
                  <div className="bg-black/10 rounded-2xl w-64 h-96 flex items-center justify-center">
                    <span className="text-white/50">{slide.phoneContent}</span>
                  </div>
                </div>
              </SplideSlide>
            ))}
          </Splide>

          {/* Text Content */}
          <div className="mt-8">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-3xl md:text-4xl font-normal mb-3 transition-opacity duration-300">
                  {slides[currentSlide].title}
                </h3>
                <p className="text-lg md:text-xl text-gray-600 max-w-2xl transition-opacity duration-300">
                  {slides[currentSlide].description}
                </p>
              </div>
              <span className="text-2xl md:text-3xl font-light text-gray-400 transition-opacity duration-300">
                {slides[currentSlide].step}
              </span>
            </div>
          </div>

          {/* Navigation Dots */}
          <div className="flex justify-center gap-3 mt-12">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  splideRef.current?.go(index); // ✅ safe call
                }}
                className={`transition-all duration-300 ${
                  index === currentSlide
                    ? "w-12 h-2 bg-teal-500 rounded-full"
                    : "w-2 h-2 bg-gray-300 rounded-full"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowItWorksSlider;
