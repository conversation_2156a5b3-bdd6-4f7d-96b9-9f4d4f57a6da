"use client";

import React from "react";

const TrustedByCarousel: React.FC = () => {
  return (
    <section className="bg-teal-800 py-12 overflow-hidden">
      <div className="flex gap-12 max-w-7xl mx-auto px-4">
        <div className="flex items-center text-white mb-8">
          <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 1L9 9L1 9L7 14L5 22L12 18L19 22L17 14L23 9L15 9L12 1Z" />
          </svg>
          <span className="text-lg ">Trusted by</span>
        </div>

        <div className="relative overflow-hidden flex-1">
          <div className="flex animate-scroll">
            {/* First set of logos */}
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Yellow Card</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Kotani Pay</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Chainlink</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">swarm</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Centrifuge</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Yellow Card</div>
            </div>

            {/* Duplicate set for seamless loop */}
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Yellow Card</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Kotani Pay</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Chainlink</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">swarm</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Centrifuge</div>
            </div>
            <div className="flex items-center justify-center min-w-[200px] px-8">
              <div className="text-white font-bold text-xl">Yellow Card</div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        .animate-scroll {
          animation: scroll 20s linear infinite;
        }
      `}</style>
    </section>
  );
};

export default TrustedByCarousel;
